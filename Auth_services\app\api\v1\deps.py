# api/v1/deps.py
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ...core.security.jwt import jwt_handler
from ...schemas.auth import CurrentUser

security = HTTPBearer(auto_error=True)

def get_current_user(credentials = Depends(security)) -> CurrentUser:
    
    token = credentials.credentials
    payload = jwt_handler.decode_access_token(token)

    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return CurrentUser(
        user_id=int(payload.get("sub")),
        username=payload.get("username"),
    )
